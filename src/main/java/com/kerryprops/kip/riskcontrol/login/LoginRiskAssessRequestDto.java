package com.kerryprops.kip.riskcontrol.login;

import com.kerryprops.kip.riskcontrol.general.TxRiskResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Login scenario risk assessment request.
 */
@Data
@Schema(description = "Login scenario risk assessment request")
public class LoginRiskAssessRequestDto {

    private String mallCode;

    @Schema(description = "Phone number", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;

    private Integer smsLoginPhoneNumberCountLast30Days;

    private TxRiskResponse txRiskResponse;
}
