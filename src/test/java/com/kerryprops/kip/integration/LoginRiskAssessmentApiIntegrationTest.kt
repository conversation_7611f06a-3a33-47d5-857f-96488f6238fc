package com.kerryprops.kip.integration

import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessRequestDto
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessmentService
import com.kerryprops.kip.riskcontrol.general.RiskResult
import com.kerryprops.kip.riskcontrol.login.AssessmentDetail
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * Comprehensive API integration tests for login risk assessment endpoint.
 * Tests the complete HTTP API behavior against Drools rules with actual service integration.
 *
 *
 * Uses @SpringBootTest with RANDOM_PORT and RestAssured for full integration testing.
 * Enables Drools rule engine to test actual business rule evaluation.
 */
@DisplayName("Login Risk Assessment API Integration Tests")
internal class LoginRiskAssessmentApiIntegrationTest : BaseIntegrationTest() {
    @Autowired
    private lateinit var loginRiskAssessmentService: LoginRiskAssessmentService

    @Test
    @DisplayName("虚拟手机号码登录全部商场都返回拒绝登录")
    fun shouldRejectVirtualPhoneNumberLoginAtAllMall() {
        LoginRiskAssessRequestDto().apply {
            mallCode = "JAKC"
            phoneNumber = "***********"
            smsLoginPhoneNumberCountLast30Days = 1
            isFrozenMember = false
        }.also {
            val response = loginRiskAssessmentService.assessRisk(it)
            assertThat(response).isNotNull()
            assertThat(response.riskResult).isEqualTo(RiskResult.REJECT)
            assertThat(response.mallCode).isEqualTo("JAKC")
            assertThat(response.assessmentTime).isNotNull
            assertThat(response.assessmentDetails).isNotNull.hasSize(1)
            assertThat(response.assessmentDetails).asList().containsExactly(
                AssessmentDetail(
                    "40001",
                    "登录：虚拟号段被拦截"
                )
            )

        }
    }
}
